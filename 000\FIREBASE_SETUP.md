# Firebase Setup for Meta Master License System

## Issue Fixed
The application was crashing with a Firebase credential error because the `meta-master-firebase.json` file contained placeholder "encrypted" values instead of actual Firebase service account credentials.

## What Was Changed
1. **Added error handling** for Firebase initialization
2. **Graceful fallback** when Firebase credentials are invalid or missing
3. **Better error messages** for users when license system is unavailable
4. **Import protection** to handle missing Firebase SDK

## How to Set Up Proper Firebase Credentials

### Step 1: Get Firebase Service Account Credentials
1. Go to your [Firebase Console](https://console.firebase.google.com/)
2. Select your project (or create a new one)
3. Navigate to **Project Settings** (gear icon) → **Service Accounts**
4. Click **"Generate new private key"**
5. Download the JSON file

### Step 2: Replace the Configuration File
1. Rename the downloaded file to `meta-master-firebase.json`
2. Replace the existing `meta-master-firebase.json` file in your project root
3. Make sure the file contains actual values, not "encrypted" placeholders

### Step 3: Verify the Configuration
The JSON file should look like this:
```json
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

### Step 4: Set Up Firestore Database
1. In Firebase Console, go to **Firestore Database**
2. Create a database (start in test mode for development)
3. Create a collection called `licenses`
4. Set up your license documents with the following structure:
```json
{
  "active": true,
  "expiry": "2024-12-31",
  "device_id": null
}
```

## Current Behavior
- **With valid Firebase credentials**: Full license validation works
- **With invalid/missing credentials**: Application runs but shows "License system unavailable" message
- **No crashes**: Application handles Firebase errors gracefully

## Security Notes
- Never commit actual Firebase credentials to version control
- Use environment variables or secure configuration management in production
- Consider using Firebase App Check for additional security
- Regularly rotate service account keys

## Testing
After setting up proper credentials, test the license system:
1. Run the application
2. Enter a test license key
3. Verify the license validation works
4. Check that device binding functions correctly
