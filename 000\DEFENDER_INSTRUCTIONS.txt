# Resolving Windows Defender False Positive Detection

If Windows Defender is detecting Meta Master as a potential threat, this is a false positive. 
Python applications packaged as executables are sometimes incorrectly flagged by antivirus software.

## Option 1: Add an Exception in Windows Defender (Recommended)

1. Click on the Windows Start button and type "Windows Security"
2. Open Windows Security
3. <PERSON>lick on "Virus & threat protection"
4. Under "Virus & threat protection settings", click on "Manage settings"
5. <PERSON><PERSON> down to "Exclusions" and click on "Add or remove exclusions"
6. <PERSON>lick on "Add an exclusion" and select "Folder"
7. <PERSON>rowse to and select the folder where Meta Master is installed (typically C:\Program Files (x86)\Meta Master)
8. Click "Select Folder" to add the exclusion

## Option 2: <PERSON>ore from Quarantine

If Windows Defender has already quarantined the file:

1. Open Windows Security
2. Click on "Virus & threat protection"
3. Under "Current threats", click on "Protection history"
4. Find the Meta Master entry in the list
5. Click on it and select "Restore"
6. Then follow the steps in Option 1 to add an exclusion

## Option 3: Use the Rebuilt Version

If you've received a rebuilt version of Meta Master with reduced false positive detection:

1. Uninstall the current version of Meta Master
2. Install the new version
3. Add an exclusion in Windows Defender as described in Option 1

## Why Does This Happen?

This is a common issue with Python applications packaged as executables. The packaging process creates patterns in the executable that some antivirus software incorrectly identifies as malicious. This is known as a "false positive" detection.

Rest assured that Meta Master is safe to use and does not contain any malicious code.

If you continue to have issues, please contact support.
