#!/usr/bin/env python3
"""
Debug script for Meta Master processing issues.
This script helps diagnose common problems with metadata generation.
"""

import os
import sys
import json
import google.generativeai as genai
from datetime import datetime

def check_api_keys():
    """Check if API keys are properly configured."""
    print("🔍 Checking API Key Configuration...")
    
    appdata_dir = os.path.join(os.getenv("APPDATA"), "MetaMaster")
    api_keys_file = os.path.join(appdata_dir, "api_keys.json")
    
    if not os.path.exists(api_keys_file):
        print("❌ No API keys file found at:", api_keys_file)
        return False
    
    try:
        with open(api_keys_file, "r") as f:
            api_keys = json.load(f)
        
        if not api_keys:
            print("❌ API keys file is empty")
            return False
        
        print(f"✅ Found {len(api_keys)} API key(s)")
        
        for i, key_data in enumerate(api_keys):
            print(f"   Key {i+1}: {key_data.get('label', 'Unlabeled')} - Status: {key_data.get('status', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading API keys: {e}")
        return False

def test_api_connection():
    """Test API connection with a simple request."""
    print("\n🧪 Testing API Connection...")
    
    appdata_dir = os.path.join(os.getenv("APPDATA"), "MetaMaster")
    api_keys_file = os.path.join(appdata_dir, "api_keys.json")
    
    try:
        with open(api_keys_file, "r") as f:
            api_keys = json.load(f)
        
        if not api_keys:
            print("❌ No API keys available for testing")
            return False
        
        # Test the first available key
        api_key = api_keys[0]["key"]
        print(f"🔑 Testing with key: {api_key[:8]}...{api_key[-4:]}")
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel("gemini-1.5-flash-latest")
        
        start_time = datetime.now()
        response = model.generate_content("Hello, this is a test. Please respond with 'API connection successful'.")
        end_time = datetime.now()
        
        elapsed = (end_time - start_time).total_seconds()
        
        if response and response.text:
            print(f"✅ API test successful in {elapsed:.2f} seconds")
            print(f"📄 Response: {response.text}")
            return True
        else:
            print("❌ API test failed: No response received")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def check_license():
    """Check license status."""
    print("\n📜 Checking License Status...")
    
    license_file = os.path.join(os.getenv("APPDATA"), "MetaMaster", "license.txt")
    
    if not os.path.exists(license_file):
        print("❌ No license file found")
        return False
    
    try:
        with open(license_file, "r") as f:
            license_key = f.read().strip()
        
        if license_key:
            print(f"✅ License key found: {license_key[:8]}...{license_key[-4:]}")
            return True
        else:
            print("❌ License file is empty")
            return False
            
    except Exception as e:
        print(f"❌ Error reading license: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed."""
    print("\n📦 Checking Dependencies...")
    
    required_modules = [
        'google.generativeai',
        'PIL',
        'ttkbootstrap',
        'firebase_admin',
        'cairosvg',
        'PyPDF2',
        'cv2'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ Missing modules: {', '.join(missing_modules)}")
        return False
    
    return True

def main():
    """Run all diagnostic checks."""
    print("🔧 Meta Master Diagnostic Tool")
    print("=" * 50)
    
    all_checks_passed = True
    
    # Check dependencies
    if not check_dependencies():
        all_checks_passed = False
    
    # Check license
    if not check_license():
        all_checks_passed = False
    
    # Check API keys
    if not check_api_keys():
        all_checks_passed = False
    else:
        # Test API connection if keys are available
        if not test_api_connection():
            all_checks_passed = False
    
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("✅ All checks passed! Meta Master should work correctly.")
    else:
        print("❌ Some issues found. Please fix the above problems.")
        print("\n💡 Common solutions:")
        print("   - Add valid API keys using the 'Manage Keys' button")
        print("   - Ensure you have a valid license key")
        print("   - Check your internet connection")
        print("   - Try the 'Test API' button in Meta Master")
    
    print("\n🔍 If processing still gets stuck on 'Processing...', check the console output for detailed error messages.")

if __name__ == "__main__":
    main()
