"""
Integrity Verification System for Meta Master
Implements file integrity checks and tamper detection
"""

import os
import sys
import hashlib
import json
import base64
from typing import Dict, List, Optional

class IntegrityVerifier:
    def __init__(self):
        self.integrity_data = self._load_integrity_data()
        self.critical_files = [
            'Meta Master.exe',
            'license_checker.py',
            'secure_config.py',
            'runtime_protection.py'
        ]
    
    def _load_integrity_data(self) -> Dict:
        """Load integrity data from embedded or external source"""
        try:
            # In production, this would be embedded in the executable
            # or fetched from a secure server
            integrity_data = {
                "version": "5.3.2",
                "build_timestamp": "2024-01-26T12:00:00Z",
                "file_hashes": {},
                "signature": ""
            }
            
            # For now, return empty structure
            return integrity_data
            
        except Exception:
            return {"file_hashes": {}, "signature": ""}
    
    def calculate_file_hash(self, file_path: str) -> Optional[str]:
        """Calculate SHA-256 hash of a file"""
        try:
            if not os.path.exists(file_path):
                return None
                
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            
            return hash_sha256.hexdigest()
            
        except Exception:
            return None
    
    def verify_file_integrity(self, file_path: str) -> bool:
        """Verify integrity of a specific file"""
        try:
            current_hash = self.calculate_file_hash(file_path)
            if not current_hash:
                return False
            
            # Get expected hash from integrity data
            filename = os.path.basename(file_path)
            expected_hash = self.integrity_data.get("file_hashes", {}).get(filename)
            
            if not expected_hash:
                # If no expected hash, consider it valid for now
                return True
            
            return current_hash == expected_hash
            
        except Exception:
            return False
    
    def verify_all_files(self) -> Dict[str, bool]:
        """Verify integrity of all critical files"""
        results = {}
        
        try:
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(__file__)
            
            for filename in self.critical_files:
                file_path = os.path.join(base_path, filename)
                results[filename] = self.verify_file_integrity(file_path)
            
            return results
            
        except Exception:
            return {filename: False for filename in self.critical_files}
    
    def generate_integrity_data(self, output_path: str = None) -> Dict:
        """Generate integrity data for current files (development use)"""
        try:
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(__file__)
            
            integrity_data = {
                "version": "5.3.2",
                "build_timestamp": "2024-01-26T12:00:00Z",
                "file_hashes": {},
                "signature": ""
            }
            
            # Calculate hashes for all files
            for filename in self.critical_files:
                file_path = os.path.join(base_path, filename)
                file_hash = self.calculate_file_hash(file_path)
                if file_hash:
                    integrity_data["file_hashes"][filename] = file_hash
            
            # Generate signature (simplified)
            data_string = json.dumps(integrity_data["file_hashes"], sort_keys=True)
            signature = hashlib.sha256(data_string.encode()).hexdigest()
            integrity_data["signature"] = signature
            
            # Save to file if requested
            if output_path:
                with open(output_path, 'w') as f:
                    json.dump(integrity_data, f, indent=2)
            
            return integrity_data
            
        except Exception as e:
            print(f"Failed to generate integrity data: {e}")
            return {}
    
    def verify_signature(self) -> bool:
        """Verify the integrity data signature"""
        try:
            file_hashes = self.integrity_data.get("file_hashes", {})
            expected_signature = self.integrity_data.get("signature", "")
            
            if not expected_signature:
                return True  # No signature to verify
            
            # Recalculate signature
            data_string = json.dumps(file_hashes, sort_keys=True)
            calculated_signature = hashlib.sha256(data_string.encode()).hexdigest()
            
            return calculated_signature == expected_signature
            
        except Exception:
            return False
    
    def perform_full_verification(self) -> Dict:
        """Perform complete integrity verification"""
        results = {
            "signature_valid": self.verify_signature(),
            "file_integrity": self.verify_all_files(),
            "overall_status": True
        }
        
        # Check overall status
        if not results["signature_valid"]:
            results["overall_status"] = False
        
        for filename, status in results["file_integrity"].items():
            if not status:
                results["overall_status"] = False
                break
        
        return results
    
    def create_integrity_report(self) -> str:
        """Create a human-readable integrity report"""
        verification_results = self.perform_full_verification()
        
        report = "=== Meta Master Integrity Report ===\n\n"
        
        # Signature verification
        sig_status = "✅ VALID" if verification_results["signature_valid"] else "❌ INVALID"
        report += f"Signature Verification: {sig_status}\n\n"
        
        # File integrity
        report += "File Integrity:\n"
        for filename, status in verification_results["file_integrity"].items():
            file_status = "✅ VALID" if status else "❌ INVALID"
            report += f"  {filename}: {file_status}\n"
        
        # Overall status
        overall_status = "✅ SECURE" if verification_results["overall_status"] else "❌ COMPROMISED"
        report += f"\nOverall Status: {overall_status}\n"
        
        if not verification_results["overall_status"]:
            report += "\n⚠️  WARNING: Application integrity has been compromised!\n"
            report += "Please reinstall from the official source.\n"
        
        return report

# Global verifier instance
integrity_verifier = IntegrityVerifier()

# Development function to generate integrity data
def generate_integrity_file():
    """Generate integrity.json file for the current build"""
    verifier = IntegrityVerifier()
    integrity_data = verifier.generate_integrity_data("integrity.json")
    print("Integrity data generated and saved to integrity.json")
    return integrity_data

if __name__ == "__main__":
    # Generate integrity data when run directly
    generate_integrity_file()
