# Meta Master Secure Installer Guide

## 🔐 Overview

This guide provides complete instructions for creating a secure installer for Meta Master that protects sensitive data and prevents easy cracking or reverse engineering.

## ✅ Security Features Implemented

### 🛡️ Client-Side Protection
- **No sensitive data exposure**: Firebase credentials never stored on client
- **Runtime protection**: Anti-debugging and anti-tampering measures
- **Code obfuscation**: Multiple layers of code protection
- **Integrity verification**: File tampering detection
- **VM detection**: Identifies virtual machine environments

### 🔒 Server-Side Security
- **Secure credential delivery**: Firebase config delivered via encrypted API
- **Request authentication**: Signed requests with app signatures
- **Rate limiting**: Prevents brute force attacks
- **Encrypted transmission**: All sensitive data encrypted in transit

### 📦 Build Security
- **Automated security checks**: Build process includes security validations
- **Sensitive file exclusion**: Automatically removes credentials from builds
- **Integrity data generation**: Creates verification checksums
- **Clean build environment**: No development artifacts in release

## 🚀 Quick Start

### 1. Run Secure Build (Windows)
```batch
deploy_secure.bat
```

### 2. Run Secure Build (Linux/Mac)
```bash
./deploy_secure.sh
```

### 3. Deploy Server Component
1. Upload `server_config_endpoint.py` to your server
2. Update Firebase credentials in the server script
3. Configure SSL/HTTPS
4. Test the endpoint

## 📋 Detailed Setup Instructions

### Step 1: Prepare Environment

1. **Install Dependencies**
   ```bash
   pip install cryptography psutil requests pyinstaller
   ```

2. **Install Inno Setup** (Windows only)
   - Download from: https://jrsoftware.org/isinfo.php
   - Install to default location

3. **Install UPX** (Optional, for additional obfuscation)
   - Download from: https://upx.github.io/
   - Add to PATH

### Step 2: Configure Server Endpoint

1. **Update Server Configuration**
   Edit `server_config_endpoint.py`:
   ```python
   FIREBASE_CONFIG = {
       "type": "service_account",
       "project_id": "your-actual-project-id",
       # ... your actual Firebase credentials
   }
   
   VALID_APP_SIGNATURES = {
       "your-app-signature": {"version": "5.1.1", "active": True}
   }
   ```

2. **Deploy to Server**
   ```bash
   # Install dependencies on server
   pip install flask cryptography
   
   # Run with gunicorn (production)
   gunicorn -w 4 -b 0.0.0.0:5000 server_config_endpoint:app
   ```

3. **Configure SSL/HTTPS**
   Set up reverse proxy with nginx or similar

### Step 3: Update Client Configuration

1. **Update Server URL**
   Edit `secure_config.py`:
   ```python
   self.config_server_url = "https://getmetamaster.com/api/config"
   ```

2. **Generate App Signature**
   The app signature is automatically generated based on executable properties

### Step 4: Build Secure Installer

1. **Run Build Script**
   ```batch
   # Windows
   deploy_secure.bat
   
   # Linux/Mac
   ./deploy_secure.sh
   ```

2. **Verify Build Output**
   - Check `Output/Meta_Master_Setup_5.1.1.exe` exists
   - Verify no sensitive files in build
   - Test installer on clean system

## 🔍 Security Verification

### Test Security Features

1. **Test Runtime Protection**
   ```python
   from runtime_protection import runtime_protection
   runtime_protection.start_protection()
   ```

2. **Test Integrity Verification**
   ```python
   from integrity_verification import integrity_verifier
   print(integrity_verifier.create_integrity_report())
   ```

3. **Test Secure Configuration**
   ```python
   from secure_config import secure_config
   config_path = secure_config.get_firebase_config_path()
   print(f"Config path: {config_path}")
   ```

### Security Checklist

- [ ] Firebase credentials removed from client build
- [ ] Server endpoint configured with SSL
- [ ] App signatures generated and validated
- [ ] Runtime protection active
- [ ] Integrity verification working
- [ ] Installer created successfully
- [ ] No sensitive data in installer
- [ ] Code obfuscation applied

## 📁 File Structure

```
Meta Master/
├── 🔐 Security Files
│   ├── secure_config.py           # Secure configuration management
│   ├── runtime_protection.py      # Runtime protection measures
│   ├── integrity_verification.py  # File integrity verification
│   └── server_config_endpoint.py  # Server-side API
│
├── 🔨 Build Files
│   ├── build_secure.py           # Secure build script
│   ├── Meta Master Secure.spec   # Secure PyInstaller spec
│   ├── Meta_Master_Installer.iss # Enhanced Inno Setup script
│   ├── deploy_secure.bat         # Windows deployment script
│   └── deploy_secure.sh          # Linux/Mac deployment script
│
├── 📚 Documentation
│   ├── SECURITY_GUIDE.md         # Comprehensive security guide
│   ├── README_SECURE_INSTALLER.md # This file
│   └── FIREBASE_SETUP.md         # Firebase setup instructions
│
└── 🚀 Application Files
    ├── Meta Master.py             # Main application
    ├── license_checker.py         # Enhanced license system
    └── ... (other app files)
```

## ⚠️ Important Security Notes

### DO NOT Include in Client Build
- `meta-master-firebase.json` (plain Firebase credentials)
- `firebase-config.json`
- `credentials.json`
- `.env` files
- Development configuration files

### Server Security Requirements
- Use HTTPS/SSL for all API endpoints
- Implement proper rate limiting
- Monitor for suspicious activity
- Regularly rotate encryption keys
- Keep server software updated

### Code Signing (Recommended)
For maximum trust, sign your installer with a code signing certificate:
1. Purchase certificate from trusted CA
2. Configure signing in Inno Setup
3. Sign both executable and installer

## 🔧 Troubleshooting

### Common Issues

1. **Build Fails**
   - Check Python and PyInstaller installation
   - Verify all dependencies installed
   - Check file permissions

2. **Installer Creation Fails**
   - Ensure Inno Setup is installed
   - Check paths in `.iss` file
   - Verify executable was built successfully

3. **Security Features Not Working**
   - Check imports in main application
   - Verify security modules are included in build
   - Test individual security components

4. **Server Connection Issues**
   - Verify server endpoint URL
   - Check SSL certificate
   - Test API endpoint manually

### Debug Mode

To test without security features:
```python
# In secure_config.py, set:
SECURITY_AVAILABLE = False

# In runtime_protection.py, set:
protection_active = False
```

## 📞 Support

For issues with the secure installer:
- Check the security guide: `SECURITY_GUIDE.md`
- Review build logs for errors
- Test individual components
- Contact support: <EMAIL>

## 🔄 Updates

To update the security system:
1. Update security modules
2. Regenerate app signatures
3. Update server configuration
4. Rebuild and test installer
5. Deploy updates

## 🎯 Next Steps

After creating the secure installer:
1. Test on multiple systems
2. Set up monitoring and logging
3. Plan regular security updates
4. Consider additional protection measures
5. Document deployment procedures

---

**Remember**: Security is an ongoing process. Regularly review and update your security measures to stay ahead of new threats.
