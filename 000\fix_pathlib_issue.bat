@echo off
REM Quick fix for pathlib PyInstaller conflict

echo ========================================
echo Meta Master - PyInstaller Pathlib Fix
echo ========================================
echo.

REM Activate virtual environment if it exists
if exist ".venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call .venv\Scripts\activate.bat
) else (
    echo No virtual environment found, using system Python
)

REM Remove conflicting pathlib package
echo Removing conflicting pathlib package...
pip uninstall pathlib -y

REM Verify removal
echo.
echo Verifying pathlib removal...
python -c "import sys; print('pathlib' in sys.modules)" 2>nul

REM Check PyInstaller
echo.
echo Checking PyInstaller installation...
python -m PyInstaller --version

if errorlevel 1 (
    echo.
    echo PyInstaller not found, installing...
    pip install pyinstaller
) else (
    echo.
    echo ✅ PyInstaller is working correctly
)

echo.
echo ========================================
echo Pathlib conflict fix completed!
echo ========================================
echo.
echo You can now run deploy_secure.bat again
echo.
pause
