# Meta Master Marketing Website

This is a modern, React-based marketing website for Meta Master, an AI-powered metadata generator for microstock contributors.

## Technologies Used

- **React.js** - Frontend library for building user interfaces
- **TypeScript** - Typed JavaScript for better developer experience
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - For navigation and routing
- **Framer Motion** - For animations and transitions
- **Headless UI** - Unstyled, accessible UI components
- **React Hook Form** - For form validation and handling
- **Heroicons** - Beautiful hand-crafted SVG icons

## Features

- **Responsive Design** - Works on all screen sizes from mobile to desktop
- **Modern UI/UX** - Dark mode with glass morphism and gradient accents
- **Animations** - Smooth animations and transitions for a premium feel
- **Interactive Elements** - Sliders, accordions, and interactive UI components
- **Form Validation** - Client-side validation for all forms
- **Payment Processing** - Simulated checkout flow with multiple payment methods
- **Accessibility** - Built with accessibility in mind

## Project Structure

- `/src/components` - Reusable UI components
  - `/layout` - Layout components (Navbar, Footer)
  - `/home` - Homepage section components
  - `/pricing` - Pricing related components
  - `/checkout` - Checkout flow components
  - `/thankyou` - Thank you page components
  - `/ui` - Shared UI components
- `/src/pages` - Page components
- `/src/hooks` - Custom React hooks
- `/src/context` - React context providers
- `/src/assets` - Static assets like images

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn install
   ```

3. Start the development server:
   ```
   npm start
   ```
   or
   ```
   yarn start
   ```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Building for Production

```
npm run build
```
or
```
yarn build
```

This will create an optimized production build in the `build` folder.

## Customization

- Update colors and theme in `tailwind.config.js`
- Modify content in the component files
- Replace placeholder images in `/src/assets/images`

## License

This project is licensed under the MIT License.
