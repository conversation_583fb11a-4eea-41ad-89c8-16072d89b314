"""
Runtime Protection and Anti-Tampering for Meta Master
Implements various security measures to prevent debugging and reverse engineering
"""

import os
import sys
import time
import psutil
import hashlib
import threading
import subprocess
from typing import List, Tuple

class RuntimeProtection:
    def __init__(self):
        self.protection_active = True
        self.check_interval = 5  # seconds
        self.suspicious_processes = [
            'ollydbg.exe', 'x64dbg.exe', 'x32dbg.exe', 'windbg.exe',
            'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe',
            'cheatengine.exe', 'processhacker.exe', 'procmon.exe',
            'wireshark.exe', 'fiddler.exe', 'charles.exe',
            'dnspy.exe', 'reflexil.exe', 'de4dot.exe',
            'ilspy.exe', 'jetbrains.dotpeek.exe'
        ]
        self.vm_indicators = [
            'vmware', 'virtualbox', 'vbox', 'qemu', 'xen',
            'parallels', 'hyper-v', 'vmtoolsd.exe', 'vboxservice.exe'
        ]
        
    def start_protection(self):
        """Start runtime protection in background thread"""
        if self.protection_active:
            protection_thread = threading.Thread(target=self._protection_loop, daemon=True)
            protection_thread.start()
    
    def _protection_loop(self):
        """Main protection loop running in background"""
        while self.protection_active:
            try:
                # Check for debugging
                if self._detect_debugger():
                    self._handle_threat("Debugger detected")
                
                # Check for suspicious processes
                suspicious = self._detect_suspicious_processes()
                if suspicious:
                    self._handle_threat(f"Suspicious process detected: {suspicious}")
                
                # Check for VM environment
                if self._detect_virtual_machine():
                    self._handle_threat("Virtual machine detected")
                
                # Check integrity
                if not self._verify_integrity():
                    self._handle_threat("Application integrity compromised")
                
                time.sleep(self.check_interval)
                
            except Exception:
                # Silently continue on errors
                time.sleep(self.check_interval)
    
    def _detect_debugger(self) -> bool:
        """Detect if debugger is attached"""
        try:
            # Check for common debugger detection methods
            
            # Method 1: Check for debugger using Windows API
            if sys.platform == "win32":
                try:
                    import ctypes
                    kernel32 = ctypes.windll.kernel32
                    if kernel32.IsDebuggerPresent():
                        return True
                except:
                    pass
            
            # Method 2: Timing-based detection
            start_time = time.perf_counter()
            # Simple operation that should be fast
            for i in range(1000):
                pass
            end_time = time.perf_counter()
            
            # If operation took too long, might be debugged
            if (end_time - start_time) > 0.01:  # 10ms threshold
                return True
            
            # Method 3: Check for debugging flags
            if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
                return True
                
            return False
            
        except:
            return False
    
    def _detect_suspicious_processes(self) -> str:
        """Detect suspicious processes that might be used for reverse engineering"""
        try:
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    for suspicious in self.suspicious_processes:
                        if suspicious.lower() in proc_name:
                            return proc_name
                except:
                    continue
            return None
        except:
            return None
    
    def _detect_virtual_machine(self) -> bool:
        """Detect if running in virtual machine"""
        try:
            # Check system manufacturer and model
            try:
                result = subprocess.check_output(
                    'wmic computersystem get manufacturer,model', 
                    shell=True, 
                    text=True
                ).lower()
                
                for indicator in self.vm_indicators:
                    if indicator in result:
                        return True
            except:
                pass
            
            # Check for VM-specific processes
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    for vm_proc in ['vmtoolsd.exe', 'vboxservice.exe', 'vboxtray.exe']:
                        if vm_proc in proc_name:
                            return True
                except:
                    continue
            
            # Check for VM-specific registry keys (Windows)
            if sys.platform == "win32":
                try:
                    import winreg
                    vm_keys = [
                        r"SOFTWARE\VMware, Inc.\VMware Tools",
                        r"SOFTWARE\Oracle\VirtualBox Guest Additions",
                        r"SYSTEM\ControlSet001\Services\VBoxGuest"
                    ]
                    
                    for key_path in vm_keys:
                        try:
                            winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                            return True
                        except:
                            continue
                except:
                    pass
            
            return False
            
        except:
            return False
    
    def _verify_integrity(self) -> bool:
        """Verify application integrity"""
        try:
            if getattr(sys, 'frozen', False):
                # Running as executable
                exe_path = sys.executable
            else:
                # Running as script
                exe_path = __file__
            
            if not os.path.exists(exe_path):
                return False
            
            # Check file size (basic integrity check)
            stat = os.stat(exe_path)
            if stat.st_size < 1000:  # Too small to be legitimate
                return False
            
            return True
            
        except:
            return False
    
    def _handle_threat(self, threat_type: str):
        """Handle detected security threat"""
        try:
            # Log the threat (in production, you might want to send to server)
            print(f"Security threat detected: {threat_type}")
            
            # Implement countermeasures
            self._apply_countermeasures(threat_type)
            
        except:
            pass
    
    def _apply_countermeasures(self, threat_type: str):
        """Apply countermeasures against detected threats"""
        try:
            if "debugger" in threat_type.lower():
                # Anti-debugging countermeasures
                self._anti_debug_measures()
            
            elif "suspicious process" in threat_type.lower():
                # Process-based countermeasures
                self._process_countermeasures()
            
            elif "virtual machine" in threat_type.lower():
                # VM-based countermeasures
                self._vm_countermeasures()
            
            elif "integrity" in threat_type.lower():
                # Integrity countermeasures
                self._integrity_countermeasures()
                
        except:
            pass
    
    def _anti_debug_measures(self):
        """Apply anti-debugging measures"""
        # Increase check frequency
        self.check_interval = max(1, self.check_interval - 1)
        
        # Add random delays to confuse debuggers
        time.sleep(0.1 + (hash(str(time.time())) % 100) / 1000)
    
    def _process_countermeasures(self):
        """Apply countermeasures for suspicious processes"""
        # Reduce functionality or add delays
        time.sleep(2)
    
    def _vm_countermeasures(self):
        """Apply countermeasures for virtual machine detection"""
        # Add delays and reduce performance in VMs
        time.sleep(1)
    
    def _integrity_countermeasures(self):
        """Apply countermeasures for integrity violations"""
        # Most severe - could terminate application
        print("Application integrity compromised. Please reinstall from official source.")
        time.sleep(5)
    
    def stop_protection(self):
        """Stop runtime protection"""
        self.protection_active = False

# Global protection instance
runtime_protection = RuntimeProtection()
