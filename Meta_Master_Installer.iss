; Inno Setup Script for Meta Master - Professional Installer

[Setup]
AppName=Meta Master
AppVersion=3.4.1
DefaultDirName={pf}\Meta Master
DefaultGroupName=Meta Master
OutputDir=E:\Software Buid\Final Meta Master With License\Final Installer
OutputBaseFilename=Meta_Master_Setup
SetupIconFile="E:\Software Buid\Final Meta Master With License\Final Build\Meta Master.ico"
Compression=lzma2
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "french"; MessagesFile: "compiler:Languages\French.isl"

[Files]
Source: "E:\Software Buid\Final Meta Master With License\Final Build\Meta Master.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "E:\Software Buid\Final Meta Master With License\Final Build\Meta Master.ico"; DestDir: "{app}"
Source: "E:\Software Buid\Final Meta Master With License\Final Build\Meta Master.png"; DestDir: "{app}"
Source: "E:\Software Buid\Final Meta Master With License\Final Build\README.txt"; DestDir: "{app}"
Source: "E:\Software Buid\Final Meta Master With License\Final Build\meta-master-firebase.json"; DestDir: "{app}"
Source: "E:\Software Buid\Final Meta Master With License\Final Build\LICENSE.txt"; DestDir: "{app}"

; External tools
Source: "E:\Software Buid\Final Meta Master With License\Final Build\"exiftool.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "E:\Software Buid\Final Meta Master With License\Final Build\"exiftool_files\*"; DestDir: "{app}\exiftool_files"; Flags: ignoreversion recursesubdirs

[Icons]
Name: "{commondesktop}\Meta Master"; Filename: "{app}\Meta Master.exe"; IconFilename: "{app}\Meta Master.ico"
Name: "{group}\Meta Master"; Filename: "{app}\Meta Master.exe"; IconFilename: "{app}\Meta Master.ico"

[Run]
Filename: "{app}\Meta Master.exe"; Description: "Launch Meta Master"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Registry]
Root: HKCU; Subkey: "Software\MetaMaster"; ValueType: string; ValueName: "Install_Dir"; ValueData: "{app}"; Flags: uninsdeletekey

[Messages]
SetupWindowTitle=Meta Master Installer
WelcomeLabel=Welcome to the Meta Master Setup Wizard.
FinishedLabel=Setup has successfully installed Meta Master.
ClickFinish=Click Finish to exit the Setup.

[Code]
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep=ssDone then
  begin
    MsgBox('Meta Master has been successfully installed! Thank you for using our software.', mbInformation, MB_OK);
  end;
end;
