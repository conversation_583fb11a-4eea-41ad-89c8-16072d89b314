import os
import sys
import firebase_admin
from firebase_admin import credentials, firestore
import datetime
import webbrowser  # Opens the WhatsApp link
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
import uuid
import getpass
import subprocess
import hashlib
import json
from google.oauth2 import service_account
from google.auth.transport.requests import AuthorizedSession

def resource_path(relative_path):
    """ Get absolute path to resource, works for both development & PyInstaller EXE """
    if getattr(sys, 'frozen', False):  # Running as an EXE
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(__file__)  # Running as Python script
    return os.path.join(base_path, relative_path)

# Fix Firebase JSON Path
firebase_json_path = resource_path("meta-master-firebase.json")

SCOPES = ["https://www.googleapis.com/auth/cloud-platform"]
SERVICE_ACCOUNT_FILE = resource_path("meta-master-firebase.json")

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE,
    scopes=SCOPES
)
authorized_session = AuthorizedSession(credentials)

# Extract project_id from the service account file
with open(SERVICE_ACCOUNT_FILE, "r") as f:
    service_account_data = json.load(f)
project_id = service_account_data["project_id"]

# Avoid repeated Firebase initialization
if not firebase_admin._apps:
    try:
        firebase_admin.initialize_app(credentials, {"projectId": project_id})
    except Exception as e:
        Messagebox.show_error("Firebase Error", f"Firebase connection failed:\n{e}")
        sys.exit()

db = firestore.client()

LICENSE_FILE = os.path.join(os.getenv("APPDATA"), "MetaMaster", "license.txt")
DEVICE_ID_FILE = os.path.join(os.getenv("APPDATA"), "MetaMaster", "device_id.txt")

def save_license_key(license_key):
    """Save the license key in a writable directory (AppData)."""
    os.makedirs(os.path.dirname(LICENSE_FILE), exist_ok=True)  # Ensure folder exists
    with open(LICENSE_FILE, "w") as f:
        f.write(license_key)

def load_license_key():
    """Retrieve the saved license key from the file."""
    if os.path.exists(LICENSE_FILE):
        with open(LICENSE_FILE, "r") as f:
            return f.read().strip()
    return None

def get_stable_device_id():
    """Generate a stable device ID using motherboard and disk serial numbers."""
    try:
        # Get motherboard serial
        motherboard_cmd = 'wmic baseboard get serialnumber'
        motherboard_serial = subprocess.check_output(motherboard_cmd, shell=True).decode().split('\n')[1].strip()

        # Get disk serial
        disk_cmd = 'wmic diskdrive get serialnumber'
        disk_serial = subprocess.check_output(disk_cmd, shell=True).decode().split('\n')[1].strip()

        combined = f"{motherboard_serial}_{disk_serial}"
        return hashlib.sha256(combined.encode()).hexdigest()
    except Exception as e:
        print(f"[Device ID Error] {e}")
        return "UNKNOWN_DEVICE"

def save_device_id(id_str):
    """Save the generated device ID locally."""
    os.makedirs(os.path.dirname(DEVICE_ID_FILE), exist_ok=True)
    with open(DEVICE_ID_FILE, "w") as f:
        f.write(id_str)

def load_device_id():
    """Load the saved device ID from local storage."""
    if os.path.exists(DEVICE_ID_FILE):
        with open(DEVICE_ID_FILE, "r") as f:
            return f.read().strip()
    return None

def check_license(license_key=None):
    """Validate the license key with Firebase Firestore and enforce single-device restriction."""
    if license_key is None:
        license_key = load_license_key()

    if not license_key:
        return False, "No License Key Found!"

    doc_ref = db.collection("licenses").document(license_key)
    doc = doc_ref.get()

    if doc.exists:
        license_data = doc.to_dict()
        is_active = license_data.get("active", False)
        expiry_date = license_data.get("expiry")
        registered_device = license_data.get("device_id", None)

        # Convert expiry date to datetime object
        expiry_date_dt = datetime.datetime.strptime(expiry_date, "%Y-%m-%d")
        today = datetime.datetime.today()
        remaining_days = (expiry_date_dt - today).days

        stored_device_id = load_device_id()
        current_device_id = get_stable_device_id()

        if not is_active:
            return False, "❌ This license has been deactivated!"

        if remaining_days <= 0:
            return False, "❌ License Expired! Please renew."

        if registered_device is None:
            # First-time activation: Bind license to this device
            doc_ref.update({"device_id": current_device_id})
            save_device_id(current_device_id)
            return True, "✅ First-time activation."

        if stored_device_id == registered_device:
            return True, "✅ License is valid (from local match)."

        if current_device_id == registered_device:
            save_device_id(current_device_id)
            return True, "✅ License matched current device."

        return False, "❌ License already used on another device!"
    else:
        return False, "❌ Invalid License Key! Contact support."

def activate_license(root, entry):
    """Allow the user to enter a license key, validate it, and restrict to one device."""
    license_key = entry.get().strip()
    current_device = get_stable_device_id()  # Get unique device ID

    print(f"🖥️ Device ID Detected: {current_device}")

    try:
        doc_ref = db.collection("licenses").document(license_key)
        doc = doc_ref.get(timeout=10)  # 10 second timeout
    except Exception as e:
        Messagebox.show_error("Activation Error", f"Failed to check license: {e}")
        return

    is_valid, message = check_license(license_key)

    if is_valid:
        # Save the license key locally
        save_license_key(license_key)

        # Update the license in Firebase with the device ID
        doc_ref.update({"device_id": current_device})  # Bind the license to this device

        Messagebox.show_info("🎉 Activation Successful!\n" + message, "License Activated")
        root.destroy()  # Close the License Window
    else:
        Messagebox.show_error(message, "Activation Failed")

def show_license_input_window():
    """Display a window to input the license key."""
    root = ttk.Window(themename="superhero")
    root.title("License Activation")
    root.geometry("400x250")
    root.resizable(False, False)

    ttk.Label(root, text="Enter Your License Key:", font=("Arial", 12, "bold")).pack(pady=10)
    entry = ttk.Entry(root, width=40)
    entry.pack(pady=5)

    btn_frame = ttk.Frame(root)
    btn_frame.pack(pady=10)

    # Create the "Contact Us" button and make it always visible
    ttk.Button(
        btn_frame, 
        text="Contact Us", 
        bootstyle=INFO, 
        command=lambda: webbrowser.open("https://www.facebook.com/m.mastersoft")
    ).pack(side=LEFT, padx=5)

    ttk.Button(
        btn_frame, 
        text="Activate", 
        bootstyle=SUCCESS, 
        command=lambda: activate_license(root, entry)
    ).pack(side=LEFT, padx=5)

    root.mainloop()

# Run License Check and Open License Window If Needed
if not check_license()[0]:  
    show_license_input_window()
