import os
import sys
import google.generativeai as genai

# Add the current directory to the path so we can import from Meta Master.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the functions from Meta Master.py
from importlib.util import spec_from_file_location, module_from_spec

# Load the Meta Master module
spec = spec_from_file_location("meta_master", os.path.join(os.path.dirname(os.path.abspath(__file__)), "Meta Master.py"))
meta_master = module_from_spec(spec)
spec.loader.exec_module(meta_master)

# Get the functions we need
generate_description_with_gemini = meta_master.generate_description_with_gemini
load_gemini_api_key = meta_master.load_gemini_api_key

def test_description_generation():
    """Test the generate_description_with_gemini function."""
    # Check if API key is available
    api_key = load_gemini_api_key()
    if not api_key:
        print("No Gemini API key found. Please set up your API key first.")
        return

    # Test with an image file
    image_file = "test_image.jpg"
    title = "Beautiful Sunset Over Mountain Lake"
    keywords = "sunset, mountains, lake, nature, landscape, reflection, water, sky, clouds, dusk, evening, peaceful, serene"

    # Generate description
    description = generate_description_with_gemini(image_file, title, keywords)
    print(f"Image Description: {description if description else 'Failed to generate description'}")
    if description:
        print(f"Length: {len(description)} characters")

    # Test with a vector file
    vector_file = "test_vector.svg"
    title = "Abstract Geometric Pattern"
    keywords = "abstract, geometric, pattern, shapes, design, modern, colorful, vector, background, decoration"

    # Generate description
    description = generate_description_with_gemini(vector_file, title, keywords)
    print(f"Vector Description: {description if description else 'Failed to generate description'}")
    if description:
        print(f"Length: {len(description)} characters")

if __name__ == "__main__":
    test_description_generation()
