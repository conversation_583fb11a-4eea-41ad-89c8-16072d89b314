"""
Secure Configuration Management for Meta Master
Handles encrypted storage and retrieval of sensitive configuration data
"""

import os
import sys
import base64
import hashlib
import requests
import json
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import tempfile
import time

class SecureConfig:
    def __init__(self):
        self.config_server_url = "https://getmetamaster.com/api/config"  # Your secure server
        self.app_signature = self._get_app_signature()
        self.device_id = self._get_device_id()
        
    def _get_app_signature(self):
        """Generate a unique signature for this application instance"""
        try:
            # Get executable path and creation time for signature
            if getattr(sys, 'frozen', False):
                exe_path = sys.executable
            else:
                exe_path = __file__
                
            stat = os.stat(exe_path)
            signature_data = f"{exe_path}:{stat.st_size}:{stat.st_mtime}"
            return hashlib.sha256(signature_data.encode()).hexdigest()[:16]
        except:
            return "DEFAULT_SIG"
    
    def _get_device_id(self):
        """Get unique device identifier"""
        try:
            import subprocess
            result = subprocess.check_output("wmic csproduct get uuid", shell=True)
            if result is None:
                return "UNKNOWN_DEVICE"

            decoded_result = result.decode()
            if not decoded_result:
                return "UNKNOWN_DEVICE"

            lines = decoded_result.split("\n")
            if len(lines) < 2:
                return "UNKNOWN_DEVICE"

            device_id = lines[1].strip()
            if not device_id:
                return "UNKNOWN_DEVICE"

            return hashlib.sha256(device_id.encode()).hexdigest()[:16]
        except:
            return "UNKNOWN_DEVICE"
    
    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password and salt"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return base64.urlsafe_b64encode(kdf.derive(password.encode()))
    
    def _encrypt_data(self, data: str, password: str) -> str:
        """Encrypt data with password"""
        salt = os.urandom(16)
        key = self._derive_key(password, salt)
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        return base64.b64encode(salt + encrypted_data).decode()
    
    def _decrypt_data(self, encrypted_data: str, password: str) -> str:
        """Decrypt data with password"""
        try:
            data = base64.b64decode(encrypted_data.encode())
            salt = data[:16]
            encrypted_content = data[16:]
            key = self._derive_key(password, salt)
            f = Fernet(key)
            return f.decrypt(encrypted_content).decode()
        except:
            return None
    
    def fetch_firebase_config(self) -> dict:
        """Fetch Firebase configuration from secure server"""
        try:
            # Prepare request with app signature and device ID
            payload = {
                "app_signature": self.app_signature,
                "device_id": self.device_id,
                "timestamp": int(time.time()),
                "config_type": "firebase"
            }
            
            # Add request signature for additional security
            request_signature = hashlib.sha256(
                f"{payload['app_signature']}:{payload['device_id']}:{payload['timestamp']}".encode()
            ).hexdigest()
            payload["signature"] = request_signature
            
            response = requests.post(
                self.config_server_url,
                json=payload,
                timeout=10,
                headers={"User-Agent": "MetaMaster/5.1.1"}
            )
            
            if response.status_code == 200:
                config_data = response.json()
                if config_data.get("success"):
                    # Decrypt the configuration
                    encrypted_config = config_data.get("config")
                    decryption_key = f"{self.app_signature}:{self.device_id}"
                    
                    firebase_config = self._decrypt_data(encrypted_config, decryption_key)
                    if firebase_config:
                        return json.loads(firebase_config)
            
            return None
            
        except Exception as e:
            print(f"Failed to fetch secure config: {e}")
            return None
    
    def create_temp_firebase_config(self, config_data: dict) -> str:
        """Create temporary Firebase config file"""
        try:
            # Create temporary file that will be deleted when app closes
            temp_dir = tempfile.gettempdir()
            temp_file = os.path.join(temp_dir, f"mm_config_{self.device_id}.tmp")
            
            with open(temp_file, 'w') as f:
                json.dump(config_data, f)
            
            # Set file to be deleted on exit
            import atexit
            atexit.register(lambda: os.remove(temp_file) if os.path.exists(temp_file) else None)
            
            return temp_file
            
        except Exception as e:
            print(f"Failed to create temp config: {e}")
            return None
    
    def get_firebase_config_path(self) -> str:
        """Get Firebase configuration file path (creates temporary file if needed)"""
        # First try to fetch from server
        config_data = self.fetch_firebase_config()
        
        if config_data:
            # Create temporary config file
            temp_path = self.create_temp_firebase_config(config_data)
            if temp_path:
                return temp_path
        
        # Fallback: check for local encrypted config (for development)
        local_config_path = self._get_local_encrypted_config()
        if local_config_path and os.path.exists(local_config_path):
            return local_config_path
            
        return None
    
    def _get_local_encrypted_config(self) -> str:
        """Get path to local encrypted configuration (development only)"""
        if getattr(sys, 'frozen', False):
            base_path = sys._MEIPASS
        else:
            base_path = os.path.dirname(__file__)
        
        return os.path.join(base_path, "config.enc")

# Global instance
secure_config = SecureConfig()
